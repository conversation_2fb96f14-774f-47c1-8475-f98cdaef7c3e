package offer

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/tmc/langchaingo/llms"
)

// CustomerSemanticAnalyzer - Analizator semantyczny komunikacji z klientem
type CustomerSemanticAnalyzer struct {
	LLM llms.Model
}

// NewCustomerSemanticAnalyzer - Konstruktor analizatora
func NewCustomerSemanticAnalyzer(llm llms.Model) *CustomerSemanticAnalyzer {
	return &CustomerSemanticAnalyzer{
		LLM: llm,
	}
}

// AnalyzeCustomer - Główna funkcja analizy klienta
func (a *CustomerSemanticAnalyzer) AnalyzeCustomer(ctx context.Context, customerID string) (*CustomerProfile, error) {
	log.Printf("Analyzing customer: %s", customerID)

	// 1. Pobierz historię emaili klienta
	emails, err := a.getCustomerEmails(customerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get customer emails: %w", err)
	}

	// 2. <PERSON><PERSON><PERSON><PERSON> email
	var emailAnalyses []EmailAnalysis
	for _, email := range emails {
		analysis, err := a.analyzeEmail(ctx, email)
		if err != nil {
			log.Printf("Failed to analyze email %s: %v", email.ID, err)
			continue
		}
		emailAnalyses = append(emailAnalyses, *analysis)
	}

	// 3. Agreguj analizy w profil klienta
	profile, err := a.buildCustomerProfile(ctx, customerID, emailAnalyses)
	if err != nil {
		return nil, fmt.Errorf("failed to build customer profile: %w", err)
	}

	log.Printf("Customer analysis completed for: %s", customerID)
	return profile, nil
}

// Email - Struktura emaila
type Email struct {
	ID       string    `json:"id"`
	From     string    `json:"from"`
	To       string    `json:"to"`
	Subject  string    `json:"subject"`
	Content  string    `json:"content"`
	Date     time.Time `json:"date"`
	ThreadID string    `json:"thread_id"`
}

// getCustomerEmails - Pobiera emaile klienta z bazy danych
func (a *CustomerSemanticAnalyzer) getCustomerEmails(customerID string) ([]Email, error) {
	// TODO: Implementacja pobierania emaili z bazy danych
	// Na razie zwracamy przykładowe dane
	return []Email{
		{
			ID:      "email_1",
			From:    "<EMAIL>",
			Subject: "Zapytanie o klimatyzację do mieszkania",
			Content: `Dzień dobry,
			
Szukam klimatyzacji do mieszkania 65m². Mam salon 25m² i sypialnię 15m². 
Zależy mi na cichej pracy i niskim zużyciu prądu. 
Budżet do 8000 zł. Czy mogą Państwo przygotować ofertę?

Pozdrawiam,
Jan Kowalski`,
			Date: time.Now().AddDate(0, 0, -5),
		},
		{
			ID:      "email_2",
			From:    "<EMAIL>",
			Subject: "Re: Zapytanie o klimatyzację",
			Content: `Dziękuję za szybką odpowiedź.
			
Interesuje mnie technologia inverter i możliwość sterowania przez WiFi.
Czy urządzenia LG mają dobrą gwarancję? 
Kiedy możliwa byłaby instalacja?

Pozdrawiam,
Jan`,
			Date: time.Now().AddDate(0, 0, -3),
		},
	}, nil
}

// analyzeEmail - Analizuje pojedynczy email
func (a *CustomerSemanticAnalyzer) analyzeEmail(ctx context.Context, email Email) (*EmailAnalysis, error) {
	// Prompt dla analizy emaila
	prompt := fmt.Sprintf(`
Przeanalizuj poniższy email od klienta HVAC i wyciągnij kluczowe informacje:

TEMAT: %s
TREŚĆ: %s

Wykonaj następujące analizy:
1. SENTIMENT: (positive/neutral/negative)
2. INTENT: (inquiry/complaint/request/follow_up)
3. TECHNICAL_TERMS: wyciągnij terminy techniczne związane z HVAC
4. PRICE_REFERENCES: znajdź wszystkie kwoty/budżety wymienione w emailu
5. ROOM_INFO: informacje o pomieszczeniach (rozmiar, typ)
6. REQUIREMENTS: wymagania techniczne klienta
7. PREFERENCES: preferencje klienta (marka, funkcje, design)
8. URGENCY: pilność zapytania (low/medium/high)

Odpowiedz w formacie JSON:
{
  "sentiment": "...",
  "intent": "...",
  "technical_terms": ["..."],
  "price_references": [1000.0, 2000.0],
  "extracted_info": {
    "room_size": "...",
    "room_count": "...",
    "budget": "...",
    "special_requirements": "...",
    "preferred_features": "...",
    "timeline": "..."
  },
  "urgency": "..."
}
`, email.Subject, email.Content)

	// Wywołanie LLM
	response, err := llms.GenerateFromSinglePrompt(ctx, a.LLM, prompt)
	if err != nil {
		return nil, fmt.Errorf("LLM analysis failed: %w", err)
	}

	// Parsowanie odpowiedzi JSON
	var analysisResult struct {
		Sentiment       string            `json:"sentiment"`
		Intent          string            `json:"intent"`
		TechnicalTerms  []string          `json:"technical_terms"`
		PriceReferences []float64         `json:"price_references"`
		ExtractedInfo   map[string]string `json:"extracted_info"`
		Urgency         string            `json:"urgency"`
	}

	if err := json.Unmarshal([]byte(response), &analysisResult); err != nil {
		// Fallback - podstawowa analiza bez LLM
		log.Printf("JSON parsing failed, using fallback analysis: %v", err)
		return a.fallbackEmailAnalysis(email), nil
	}

	// Tworzenie struktury EmailAnalysis
	analysis := &EmailAnalysis{
		EmailID:         email.ID,
		Date:            email.Date,
		Subject:         email.Subject,
		Content:         email.Content,
		Sentiment:       analysisResult.Sentiment,
		Intent:          analysisResult.Intent,
		ExtractedInfo:   analysisResult.ExtractedInfo,
		TechnicalTerms:  analysisResult.TechnicalTerms,
		PriceReferences: analysisResult.PriceReferences,
	}

	return analysis, nil
}

// fallbackEmailAnalysis - Podstawowa analiza bez LLM
func (a *CustomerSemanticAnalyzer) fallbackEmailAnalysis(email Email) *EmailAnalysis {
	content := strings.ToLower(email.Content)

	// Podstawowa analiza sentymentu
	sentiment := "neutral"
	if strings.Contains(content, "dziękuję") || strings.Contains(content, "świetnie") {
		sentiment = "positive"
	} else if strings.Contains(content, "problem") || strings.Contains(content, "nie działa") {
		sentiment = "negative"
	}

	// Podstawowa analiza intencji
	intent := "inquiry"
	if strings.Contains(content, "oferta") || strings.Contains(content, "zapytanie") {
		intent = "request"
	}

	// Wyciąganie kwot
	priceRegex := regexp.MustCompile(`(\d+)\s*zł`)
	matches := priceRegex.FindAllStringSubmatch(content, -1)
	var prices []float64
	for _, match := range matches {
		if price, err := strconv.ParseFloat(match[1], 64); err == nil {
			prices = append(prices, price)
		}
	}

	// Podstawowe terminy techniczne
	technicalTerms := []string{}
	hvacTerms := []string{"klimatyzacja", "inverter", "split", "wifi", "cicha praca", "zużycie prądu"}
	for _, term := range hvacTerms {
		if strings.Contains(content, term) {
			technicalTerms = append(technicalTerms, term)
		}
	}

	return &EmailAnalysis{
		EmailID:         email.ID,
		Date:            email.Date,
		Subject:         email.Subject,
		Content:         email.Content,
		Sentiment:       sentiment,
		Intent:          intent,
		ExtractedInfo:   make(map[string]string),
		TechnicalTerms:  technicalTerms,
		PriceReferences: prices,
	}
}

// buildCustomerProfile - Buduje profil klienta na podstawie analiz emaili
func (a *CustomerSemanticAnalyzer) buildCustomerProfile(ctx context.Context, customerID string, emailAnalyses []EmailAnalysis) (*CustomerProfile, error) {
	// Agregacja danych z wszystkich emaili
	allTechnicalTerms := []string{}
	allPrices := []float64{}
	communicationTone := "formal"

	for _, analysis := range emailAnalyses {
		allTechnicalTerms = append(allTechnicalTerms, analysis.TechnicalTerms...)
		allPrices = append(allPrices, analysis.PriceReferences...)

		// Analiza tonu komunikacji
		if strings.Contains(strings.ToLower(analysis.Content), "cześć") ||
			strings.Contains(strings.ToLower(analysis.Content), "dzięki") {
			communicationTone = "casual"
		}
	}

	// Prompt dla LLM do stworzenia profilu
	prompt := fmt.Sprintf(`
Na podstawie analizy emaili klienta, stwórz kompletny profil klienta HVAC:

ANALIZA EMAILI:
%s

TERMINY TECHNICZNE: %v
KWOTY WYMIENIONE: %v
TON KOMUNIKACJI: %s

Stwórz profil klienta w formacie JSON:
{
  "requirements": {
    "room_size": 0.0,
    "room_count": 0,
    "building_type": "apartment/house/office",
    "installation_type": "wall/ceiling/floor",
    "special_features": ["wifi", "inverter", "dual_cool"],
    "energy_class": "A+++",
    "noise_level": "low"
  },
  "budget": {
    "min_price": 0.0,
    "max_price": 0.0,
    "currency": "PLN",
    "flexible": true
  },
  "preferences": {
    "brand": "LG",
    "technology": ["inverter", "wifi"],
    "design": "modern",
    "installation_time": "flexible",
    "warranty": 5
  },
  "semantic_keywords": ["cicha praca", "oszczędność energii", "wifi"]
}
`, formatEmailAnalyses(emailAnalyses), allTechnicalTerms, allPrices, communicationTone)

	// Wywołanie LLM
	response, err := llms.GenerateFromSinglePrompt(ctx, a.LLM, prompt)
	if err != nil {
		return nil, fmt.Errorf("LLM profile generation failed: %w", err)
	}

	// Parsowanie odpowiedzi
	var profileData struct {
		Requirements     CustomerRequirements `json:"requirements"`
		Budget           BudgetRange          `json:"budget"`
		Preferences      CustomerPreferences  `json:"preferences"`
		SemanticKeywords []string             `json:"semantic_keywords"`
	}

	if err := json.Unmarshal([]byte(response), &profileData); err != nil {
		// Fallback - podstawowy profil
		return a.createFallbackProfile(customerID, emailAnalyses, communicationTone), nil
	}

	// Tworzenie kompletnego profilu
	profile := &CustomerProfile{
		CustomerID:        customerID,
		Name:              extractCustomerName(emailAnalyses),
		Email:             extractCustomerEmail(emailAnalyses),
		CommunicationTone: communicationTone,
		Requirements:      profileData.Requirements,
		Budget:            profileData.Budget,
		Preferences:       profileData.Preferences,
		EmailHistory:      emailAnalyses,
		SemanticKeywords:  profileData.SemanticKeywords,
	}

	return profile, nil
}

// formatEmailAnalyses - Formatuje analizy emaili do stringa
func formatEmailAnalyses(analyses []EmailAnalysis) string {
	var result strings.Builder
	for i, analysis := range analyses {
		result.WriteString(fmt.Sprintf("EMAIL %d:\n", i+1))
		result.WriteString(fmt.Sprintf("Temat: %s\n", analysis.Subject))
		result.WriteString(fmt.Sprintf("Sentiment: %s\n", analysis.Sentiment))
		result.WriteString(fmt.Sprintf("Intent: %s\n", analysis.Intent))
		result.WriteString(fmt.Sprintf("Treść: %s\n\n", analysis.Content))
	}
	return result.String()
}

// extractCustomerName - Wyciąga imię klienta z emaili
func extractCustomerName(analyses []EmailAnalysis) string {
	for _, analysis := range analyses {
		// Szukaj podpisu w emailu
		lines := strings.Split(analysis.Content, "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if strings.Contains(line, "Pozdrawiam") && len(line) < 50 {
				// Następna linia może zawierać imię
				continue
			}
			if len(line) > 0 && len(line) < 30 && !strings.Contains(line, "@") {
				return line
			}
		}
	}
	return "Szanowny Kliencie"
}

// extractCustomerEmail - Wyciąga email klienta
func extractCustomerEmail(analyses []EmailAnalysis) string {
	if len(analyses) > 0 {
		// Zakładamy, że pierwszy email zawiera adres nadawcy
		return "<EMAIL>" // TODO: Implementacja wyciągania z bazy danych
	}
	return ""
}

// createFallbackProfile - Tworzy podstawowy profil w przypadku błędu LLM
func (a *CustomerSemanticAnalyzer) createFallbackProfile(customerID string, analyses []EmailAnalysis, tone string) *CustomerProfile {
	return &CustomerProfile{
		CustomerID:        customerID,
		Name:              extractCustomerName(analyses),
		Email:             extractCustomerEmail(analyses),
		CommunicationTone: tone,
		Requirements: CustomerRequirements{
			RoomSize:         50.0,
			RoomCount:        2,
			BuildingType:     "apartment",
			InstallationType: "wall",
			SpecialFeatures:  []string{"inverter", "wifi"},
			EnergyClass:      "A++",
			NoiseLevel:       "low",
		},
		Budget: BudgetRange{
			MinPrice: 5000.0,
			MaxPrice: 10000.0,
			Currency: "PLN",
			Flexible: true,
		},
		Preferences: CustomerPreferences{
			Brand:            "LG",
			Technology:       []string{"inverter", "wifi"},
			Design:           "modern",
			InstallationTime: "flexible",
			Warranty:         5,
		},
		EmailHistory:     analyses,
		SemanticKeywords: []string{"klimatyzacja", "cicha praca", "oszczędność"},
	}
}
